# Augment eklentisi sorun giderme scripti

param(
    [switch]$FullReset,
    [switch]$ClearCache,
    [switch]$RestartOnly
)

Write-Host "=== Augment Eklentisi Sorun Giderme ===" -ForegroundColor Cyan

if ($FullReset) {
    Write-Host "Tam sıfırlama yapılıyor..." -ForegroundColor Yellow
    
    # VS Code işlemlerini kapat
    Get-Process -Name "Code" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 3
    
    # Augment cache'ini temizle
    $augmentCache = "$env:APPDATA\Code\User\workspaceStorage"
    if (Test-Path $augmentCache) {
        Write-Host "Augment cache temizleniyor..." -ForegroundColor Yellow
        Remove-Item "$augmentCache\*augment*" -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    # Extension'ı yeniden yükle
    code --uninstall-extension augment.vscode-augment
    Start-Sleep -Seconds 2
    code --install-extension augment.vscode-augment
    Start-Sleep -Seconds 3
}

if ($ClearCache) {
    Write-Host "Cache temizleniyor..." -ForegroundColor Yellow
    
    # VS Code cache'ini temizle
    $vscodeCache = "$env:APPDATA\Code\CachedExtensions"
    if (Test-Path $vscodeCache) {
        Remove-Item "$vscodeCache\*" -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    # Workspace storage temizle
    $workspaceStorage = "$env:APPDATA\Code\User\workspaceStorage"
    if (Test-Path $workspaceStorage) {
        Get-ChildItem $workspaceStorage | Where-Object { $_.Name -like "*augment*" } | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# VS Code'u yeniden başlat
Write-Host "VS Code yeniden başlatılıyor..." -ForegroundColor Green
Get-Process -Name "Code" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# Yeni VS Code instance başlat
code . --new-window

Write-Host "İşlem tamamlandı!" -ForegroundColor Green
Write-Host "Kullanım:" -ForegroundColor Cyan
Write-Host "  .\fix-augment.ps1                 # Normal yeniden başlatma" -ForegroundColor White
Write-Host "  .\fix-augment.ps1 -ClearCache     # Cache temizleyerek yeniden başlat" -ForegroundColor White
Write-Host "  .\fix-augment.ps1 -FullReset      # Tam sıfırlama (extension yeniden yükle)" -ForegroundColor White

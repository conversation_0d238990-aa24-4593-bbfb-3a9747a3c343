# Hastane projesi için Augment sorun giderme scripti

param(
    [string]$HastanePath = "C:\Documents\ITB_SOURCE_4.8\Hastane",
    [switch]$ResetIndex,
    [switch]$SlowMode,
    [switch]$DisableAugment
)

Write-Host "=== Hastane Projesi Augment Sorun Giderme ===" -ForegroundColor Cyan

if ($DisableAugment) {
    Write-Host "Augment eklentisi devre dışı bırakılıyor..." -ForegroundColor Yellow
    code --disable-extension augment.vscode-augment
    Write-Host "Augment devre dışı bırakıldı. Normal VS Code kullanabilirsiniz." -ForegroundColor Green
    exit 0
}

# VS Code'u kapat
Write-Host "VS Code kapatılıyor..." -ForegroundColor Yellow
Get-Process -Name "Code" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 5

if ($ResetIndex) {
    Write-Host "Augment index sıfırlanıyor..." -ForegroundColor Yellow
    
    # Tüm Augment cache'ini temizle
    $cacheLocations = @(
        "$env:APPDATA\Code\User\workspaceStorage",
        "$env:APPDATA\Code\CachedExtensions",
        "$env:LOCALAPPDATA\augment",
        "$env:TEMP\augment*"
    )
    
    foreach ($location in $cacheLocations) {
        if (Test-Path $location) {
            Write-Host "Temizleniyor: $location" -ForegroundColor Gray
            if ($location -like "*workspaceStorage*") {
                Get-ChildItem $location | Where-Object { 
                    $_.Name -like "*hastane*" -or 
                    $_.Name -like "*ITB*" -or 
                    $_.Name -like "*augment*" 
                } | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
            } else {
                Remove-Item $location -Recurse -Force -ErrorAction SilentlyContinue
            }
        }
    }
}

# Memory temizliği
Write-Host "Sistem kaynakları temizleniyor..." -ForegroundColor Yellow
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()
[System.GC]::Collect()

# Yavaş mod ayarları
if ($SlowMode) {
    Write-Host "Yavaş mod ayarları uygulanıyor..." -ForegroundColor Yellow
    
    $slowSettings = @"
{
  "augment.autoStart": false,
  "augment.enableLogging": true,
  "augment.maxMemoryUsage": 4096,
  "augment.timeout": 300000,
  "augment.indexingTimeout": 600000,
  "augment.retryAttempts": 0,
  "augment.batchSize": 10,
  "augment.indexingDelay": 1000,
  "files.watcherExclude": {
    "**/.git/**": true,
    "**/node_modules/**": true,
    "**/bin/**": true,
    "**/obj/**": true,
    "**/packages/**": true,
    "**/Debug/**": true,
    "**/Release/**": true,
    "**/.vs/**": true,
    "**/logs/**": true,
    "**/audio/**": true,
    "**/Bootstrap----/**": true,
    "**/ckeditor/**": true,
    "**/*.dll": true,
    "**/*.exe": true,
    "**/*.pdb": true,
    "**/*.log": true
  },
  "search.exclude": {
    "**/bin": true,
    "**/obj": true,
    "**/packages": true,
    "**/Debug": true,
    "**/Release": true,
    "**/.vs": true,
    "**/logs": true,
    "**/audio": true,
    "**/Bootstrap----": true,
    "**/ckeditor": true
  }
}
"@
    
    $settingsPath = Join-Path $HastanePath ".vscode\settings.json"
    $slowSettings | Out-File -FilePath $settingsPath -Encoding UTF8 -Force
    Write-Host "Yavaş mod ayarları uygulandı." -ForegroundColor Green
}

# Sistem durumunu kontrol et
$memoryUsage = Get-WmiObject -Class Win32_OperatingSystem | Select-Object @{Name="FreeMemoryGB";Expression={[math]::Round($_.FreePhysicalMemory/1MB,2)}}
Write-Host "Mevcut boş RAM: $($memoryUsage.FreeMemoryGB) GB" -ForegroundColor Cyan

if ($memoryUsage.FreeMemoryGB -lt 2) {
    Write-Host "UYARI: Düşük RAM! Yavaş mod önerilir." -ForegroundColor Red
    Write-Host "Yavaş mod için: .\fix-hastane-augment.ps1 -SlowMode" -ForegroundColor Yellow
}

# VS Code'u yeniden başlat
Write-Host "VS Code yeniden başlatılıyor..." -ForegroundColor Green
Write-Host "İPUCU: Augment'in indexleme tamamlamasını bekleyin (5-15 dakika)" -ForegroundColor Yellow

Start-Process "code" -ArgumentList "`"$HastanePath`"", "--new-window" -NoNewWindow

Write-Host "`n=== Sorun Giderme Tamamlandı ===" -ForegroundColor Cyan
Write-Host "Kullanım seçenekleri:" -ForegroundColor Yellow
Write-Host "  .\fix-hastane-augment.ps1                    # Normal sorun giderme" -ForegroundColor White
Write-Host "  .\fix-hastane-augment.ps1 -ResetIndex        # Index sıfırlama" -ForegroundColor White
Write-Host "  .\fix-hastane-augment.ps1 -SlowMode          # Yavaş mod (düşük RAM)" -ForegroundColor White
Write-Host "  .\fix-hastane-augment.ps1 -DisableAugment    # Augment'i devre dışı bırak" -ForegroundColor White

Write-Host "`nEğer sorun devam ederse:" -ForegroundColor Cyan
Write-Host "1. Task Manager'dan Code.exe işlemlerini kontrol edin" -ForegroundColor White
Write-Host "2. RAM kullanımını izleyin" -ForegroundColor White
Write-Host "3. Gerekirse -DisableAugment ile çalışın" -ForegroundColor White

# VS Code ve Augment eklentisini yeniden başlatma scripti

Write-Host "VS Code işlemlerini durduruyor..." -ForegroundColor Yellow

# VS Code işlemlerini kapat
Get-Process -Name "Code" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# Augment eklentisini devre dışı bırak ve tekrar etkinleştir
Write-Host "Augment eklentisini yeniden başlatıyor..." -ForegroundColor Yellow
code --disable-extension augment.vscode-augment
Start-Sleep -Seconds 1
code --enable-extension augment.vscode-augment
Start-Sleep -Seconds 1

# VS Code'u mevcut klasörle başlat
Write-Host "VS Code'u başlatıyor..." -ForegroundColor Green
code . --new-window

Write-Host "İşlem tamamlandı!" -ForegroundColor Green
Write-Host "VS Code yeniden başlatıldı ve Augment eklentisi sıfırlandı." -ForegroundColor Cyan

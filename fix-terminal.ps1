# VS Code Terminal sorunlarını çözme scripti

Write-Host "=== VS CODE TERMINAL SORUN GİDERME ===" -ForegroundColor Cyan

# 1. Mevcut terminal ayarlarını kontrol et
Write-Host "1. Mevcut terminal ayarları kontrol ediliyor..." -ForegroundColor Yellow

$globalSettingsPath = "$env:APPDATA\Code\User\settings.json"
if (-not (Test-Path $globalSettingsPath)) {
    Write-Host "❌ VS Code settings.json bulunamadı!" -ForegroundColor Red
    exit 1
}

# 2. PowerShell yollarını kontrol et
Write-Host "2. PowerShell yolları kontrol ediliyor..." -ForegroundColor Yellow

$powershellPaths = @(
    "C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe",
    "C:\Program Files\PowerShell\7\pwsh.exe",
    "$env:WINDIR\System32\WindowsPowerShell\v1.0\powershell.exe"
)

$validPowershellPath = $null
foreach ($path in $powershellPaths) {
    if (Test-Path $path) {
        $validPowershellPath = $path
        Write-Host "✅ PowerShell bulundu: $path" -ForegroundColor Green
        break
    }
}

if (-not $validPowershellPath) {
    Write-Host "❌ PowerShell bulunamadı!" -ForegroundColor Red
    exit 1
}

# 3. CMD yolunu kontrol et
$cmdPath = "C:\Windows\System32\cmd.exe"
if (-not (Test-Path $cmdPath)) {
    Write-Host "❌ CMD bulunamadı!" -ForegroundColor Red
    exit 1
}
Write-Host "✅ CMD bulundu: $cmdPath" -ForegroundColor Green

# 4. VS Code ayarlarını düzelt
Write-Host "3. VS Code terminal ayarları düzeltiliyor..." -ForegroundColor Yellow

try {
    $settings = Get-Content $globalSettingsPath -Raw | ConvertFrom-Json
    
    # Terminal profilleri düzelt
    $terminalProfiles = @{
        "PowerShell" = @{
            "path" = $validPowershellPath
            "args" = @("-NoLogo")
            "icon" = "terminal-powershell"
        }
        "Command Prompt" = @{
            "path" = $cmdPath
            "args" = @()
            "icon" = "terminal-cmd"
        }
        "Git Bash" = @{
            "source" = "Git Bash"
        }
    }
    
    # Ayarları güncelle
    $settings | Add-Member -NotePropertyName "terminal.integrated.profiles.windows" -NotePropertyValue $terminalProfiles -Force
    $settings | Add-Member -NotePropertyName "terminal.integrated.defaultProfile.windows" -NotePropertyValue "PowerShell" -Force
    $settings | Add-Member -NotePropertyName "terminal.integrated.shellIntegration.enabled" -NotePropertyValue $true -Force
    $settings | Add-Member -NotePropertyName "terminal.integrated.enableMultiLinePasteWarning" -NotePropertyValue $false -Force
    $settings | Add-Member -NotePropertyName "terminal.integrated.copyOnSelection" -NotePropertyValue $true -Force
    $settings | Add-Member -NotePropertyName "terminal.integrated.rightClickBehavior" -NotePropertyValue "paste" -Force
    $settings | Add-Member -NotePropertyName "terminal.integrated.showExitAlert" -NotePropertyValue $false -Force
    $settings | Add-Member -NotePropertyName "terminal.integrated.persistentSessionReviveProcess" -NotePropertyValue "never" -Force
    $settings | Add-Member -NotePropertyName "terminal.integrated.enablePersistentSessions" -NotePropertyValue $false -Force
    
    # Ayarları kaydet
    $settings | ConvertTo-Json -Depth 10 | Out-File -FilePath $globalSettingsPath -Encoding UTF8 -Force
    Write-Host "✅ Terminal ayarları düzeltildi." -ForegroundColor Green
    
} catch {
    Write-Host "❌ Ayarlar güncellenirken hata: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 5. PowerShell profilini kontrol et
Write-Host "4. PowerShell profili kontrol ediliyor..." -ForegroundColor Yellow

$profilePath = $PROFILE
if (Test-Path $profilePath) {
    Write-Host "✅ PowerShell profili mevcut: $profilePath" -ForegroundColor Green
    
    # Profilde sorunlu komutları kontrol et
    $profileContent = Get-Content $profilePath -Raw -ErrorAction SilentlyContinue
    if ($profileContent -and ($profileContent -match "error|exception|fail")) {
        Write-Host "⚠️  PowerShell profilinde potansiyel sorun tespit edildi." -ForegroundColor Yellow
        Write-Host "Profil yedekleniyor..." -ForegroundColor Gray
        Copy-Item $profilePath "$profilePath.backup" -Force
        Write-Host "✅ Profil yedeklendi: $profilePath.backup" -ForegroundColor Green
    }
} else {
    Write-Host "ℹ️  PowerShell profili yok (normal)." -ForegroundColor Gray
}

# 6. Hastane klasörü terminal ayarlarını düzelt
Write-Host "5. Hastane klasörü terminal ayarları düzeltiliyor..." -ForegroundColor Yellow

$hastaneSettingsPath = "C:\Documents\ITB_SOURCE_4.8\Hastane\.vscode\settings.json"
if (Test-Path $hastaneSettingsPath) {
    try {
        $hastaneSettings = Get-Content $hastaneSettingsPath -Raw | ConvertFrom-Json
        
        # Terminal ayarlarını ekle
        $hastaneSettings | Add-Member -NotePropertyName "terminal.integrated.defaultProfile.windows" -NotePropertyValue "PowerShell" -Force
        $hastaneSettings | Add-Member -NotePropertyName "terminal.integrated.shellIntegration.enabled" -NotePropertyValue $true -Force
        $hastaneSettings | Add-Member -NotePropertyName "terminal.integrated.showExitAlert" -NotePropertyValue $false -Force
        
        # Kaydet
        $hastaneSettings | ConvertTo-Json -Depth 10 | Out-File -FilePath $hastaneSettingsPath -Encoding UTF8 -Force
        Write-Host "✅ Hastane klasörü terminal ayarları düzeltildi." -ForegroundColor Green
        
    } catch {
        Write-Host "⚠️  Hastane ayarları güncellenirken hata (devam ediliyor)." -ForegroundColor Yellow
    }
}

# 7. Terminal cache'ini temizle
Write-Host "6. Terminal cache'i temizleniyor..." -ForegroundColor Yellow

$terminalCacheLocations = @(
    "$env:APPDATA\Code\User\workspaceStorage",
    "$env:APPDATA\Code\logs"
)

foreach ($location in $terminalCacheLocations) {
    if (Test-Path $location) {
        Get-ChildItem $location -Recurse -ErrorAction SilentlyContinue | 
        Where-Object { $_.Name -like "*terminal*" -or $_.Name -like "*shell*" } | 
        Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
    }
}
Write-Host "✅ Terminal cache'i temizlendi." -ForegroundColor Green

Write-Host "`n=== TERMINAL SORUN GİDERME TAMAMLANDI ===" -ForegroundColor Green
Write-Host "✅ PowerShell yolu düzeltildi" -ForegroundColor White
Write-Host "✅ Terminal profilleri güncellendi" -ForegroundColor White
Write-Host "✅ Default profile PowerShell olarak ayarlandı" -ForegroundColor White
Write-Host "✅ Terminal ayarları optimize edildi" -ForegroundColor White
Write-Host "✅ Cache temizlendi" -ForegroundColor White

Write-Host "`nŞimdi VS Code'da:" -ForegroundColor Cyan
Write-Host "1. Ctrl+Shift+P > 'Developer: Reload Window'" -ForegroundColor White
Write-Host "2. Ctrl+` (backtick) ile terminal açmayı deneyin" -ForegroundColor White
Write-Host "3. Eğer sorun devam ederse, VS Code'u yeniden başlatın" -ForegroundColor White

Write-Host "`nTest komutu:" -ForegroundColor Yellow
Write-Host "code --version" -ForegroundColor Gray

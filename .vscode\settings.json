{
  // Terminal ayarları
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.profiles.windows": {
    "PowerShell": {
      "source": "PowerShell",
      "icon": "terminal-powershell",
      "args": [
        "-NoLogo"
      ]
    },
    "Command Prompt": {
      "path": [
        "${env:windir}\\Sysnative\\cmd.exe",
        "${env:windir}\\System32\\cmd.exe"
      ],
      "args": [],
      "icon": "terminal-cmd"
    }
  },
  "terminal.integrated.shellIntegration.enabled": true,
  "terminal.integrated.enableMultiLinePasteWarning": false,
  "terminal.integrated.copyOnSelection": true,
  "terminal.integrated.rightClickBehavior": "paste",
  "terminal.integrated.persistentSessionReviveProcess": "never",
  "terminal.integrated.enablePersistentSessions": false,
  "terminal.integrated.showExitAlert": false,
  // Performans ayarları
  "extensions.autoUpdate": false,
  "extensions.autoCheckUpdates": false,
  "workbench.startupEditor": "none",
  "window.restoreWindows": "none",
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/tmp/**": true,
    "**/bower_components/**": true,
    "**/dist/**": true,
    "**/build/**": true
  },
  // Augment eklentisi için ayarlar
  "augment.autoStart": true,
  "augment.enableLogging": true,
  "augment.maxMemoryUsage": 4096,
  "augment.restartOnError": false,
  "augment.timeout": 30000,
  "augment.retryAttempts": 3,
  // Genel editor ayarları
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true
  },
  "files.autoSave": "afterDelay",
  "files.autoSaveDelay": 1000,
  // Git ayarları
  "git.autofetch": false,
  "git.confirmSync": false,
  "git.enableSmartCommit": true,
  // Diğer performans ayarları
  "search.followSymlinks": false,
  "typescript.surveys.enabled": false,
  "telemetry.telemetryLevel": "off",
  "update.mode": "manual"
}
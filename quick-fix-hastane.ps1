# Hastane projesi hızlı çözüm scripti

Write-Host "=== Hastane Projesi Hızlı Çözüm ===" -ForegroundColor Cyan

$HastanePath = "C:\Documents\ITB_SOURCE_4.8\Hastane"

# 1. Tüm VS Code işlemlerini zorla kapat
Write-Host "1. VS Code işlemleri kapatılıyor..." -ForegroundColor Yellow
Get-Process -Name "*Code*" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 3

# 2. Augment cache'ini tamamen temizle
Write-Host "2. Augment cache tamamen temizleniyor..." -ForegroundColor Yellow
$cacheLocations = @(
    "$env:APPDATA\Code\User\workspaceStorage",
    "$env:APPDATA\Code\CachedExtensions",
    "$env:APPDATA\Code\logs",
    "$env:LOCALAPPDATA\augment",
    "$env:TEMP\vscode*",
    "$env:TEMP\augment*"
)

foreach ($location in $cacheLocations) {
    if (Test-Path $location) {
        if ($location -like "*workspaceStorage*") {
            # Sadece Hastane ile ilgili cache'leri temizle
            Get-ChildItem $location -ErrorAction SilentlyContinue | Where-Object { 
                $_.Name -like "*hastane*" -or 
                $_.Name -like "*ITB*" -or 
                $_.Name -like "*augment*" 
            } | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
        } else {
            Remove-Item $location -Recurse -Force -ErrorAction SilentlyContinue
        }
        Write-Host "   Temizlendi: $location" -ForegroundColor Gray
    }
}

# 3. Memory temizliği
Write-Host "3. Sistem memory temizleniyor..." -ForegroundColor Yellow
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()
[System.GC]::Collect()

# 4. Augment'i geçici olarak devre dışı bırak
Write-Host "4. Augment eklentisi devre dışı bırakılıyor..." -ForegroundColor Yellow
code --disable-extension augment.vscode-augment

# 5. VS Code'u minimal ayarlarla başlat
Write-Host "5. VS Code minimal ayarlarla başlatılıyor..." -ForegroundColor Green
Start-Process "code" -ArgumentList "`"$HastanePath`"", "--disable-extension", "augment.vscode-augment", "--new-window", "--disable-gpu"

# 6. VS Code'un başlamasını bekle
Write-Host "6. VS Code'un başlaması bekleniyor..." -ForegroundColor Yellow
$attempts = 0
do {
    Start-Sleep -Seconds 2
    $codeRunning = Get-Process -Name "Code" -ErrorAction SilentlyContinue
    $attempts++
    Write-Host "." -NoNewline -ForegroundColor Gray
    
    if ($attempts -gt 20) {
        Write-Host "`nTIMEOUT: VS Code başlatılamadı!" -ForegroundColor Red
        Write-Host "Manuel olarak VS Code'u başlatmayı deneyin:" -ForegroundColor Yellow
        Write-Host "code `"$HastanePath`" --disable-extension augment.vscode-augment" -ForegroundColor White
        exit 1
    }
} while (-not $codeRunning)

Write-Host "`n✅ VS Code başarıyla başlatıldı!" -ForegroundColor Green

# 7. Kullanıcıya seçenekleri sun
Write-Host "`n=== SONRAKI ADIMLAR ===" -ForegroundColor Cyan
Write-Host "VS Code şu anda Augment OLMADAN çalışıyor." -ForegroundColor Yellow
Write-Host "`nSeçenekleriniz:" -ForegroundColor White
Write-Host "A) Augment olmadan devam et (önerilen)" -ForegroundColor Green
Write-Host "B) Augment'i yavaşça etkinleştir" -ForegroundColor Yellow
Write-Host "C) Hiçbir şey yapma" -ForegroundColor Gray

$choice = Read-Host "`nSeçiminizi yapın (A/B/C)"

switch ($choice.ToUpper()) {
    "A" {
        Write-Host "`n✅ Augment olmadan devam ediyorsunuz." -ForegroundColor Green
        Write-Host "Augment'i tekrar etkinleştirmek için:" -ForegroundColor Cyan
        Write-Host "code --enable-extension augment.vscode-augment" -ForegroundColor White
    }
    "B" {
        Write-Host "`n⏳ Augment yavaşça etkinleştiriliyor..." -ForegroundColor Yellow
        Write-Host "Bu işlem 1-2 dakika sürebilir ve VS Code yeniden başlayabilir." -ForegroundColor Gray
        
        Start-Sleep -Seconds 5
        code --enable-extension augment.vscode-augment
        
        Write-Host "✅ Augment etkinleştirildi." -ForegroundColor Green
        Write-Host "Eğer VS Code kapanırsa, tekrar A seçeneğini kullanın." -ForegroundColor Yellow
    }
    "C" {
        Write-Host "`n✅ Hiçbir değişiklik yapılmadı." -ForegroundColor Green
    }
    default {
        Write-Host "`n✅ Geçersiz seçim. Augment olmadan devam ediyorsunuz." -ForegroundColor Green
    }
}

Write-Host "`n=== HIZLI ÇÖZÜM TAMAMLANDI ===" -ForegroundColor Cyan
Write-Host "Eğer sorun devam ederse:" -ForegroundColor Yellow
Write-Host "1. .\diagnose-vscode-crash.ps1  # Detaylı tanılama" -ForegroundColor White
Write-Host "2. Bilgisayarı yeniden başlatın" -ForegroundColor White
Write-Host "3. VS Code'u güncelleyin" -ForegroundColor White

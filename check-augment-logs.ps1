# Augment eklentisi log kontrolü

Write-Host "=== Augment Eklentisi Log Kontrolü ===" -ForegroundColor Cyan

# VS Code log dizinini kontrol et
$vscodeLogPath = "$env:APPDATA\Code\logs"
if (Test-Path $vscodeLogPath) {
    Write-Host "VS Code log dizini: $vscodeLogPath" -ForegroundColor Green
    
    # En son log dosyasını bul
    $latestLog = Get-ChildItem $vscodeLogPath | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    if ($latestLog) {
        Write-Host "En son log: $($latestLog.FullName)" -ForegroundColor Yellow
        
        # Augment ile ilgili hataları ara
        $augmentErrors = Get-Content "$($latestLog.FullName)\exthost\exthost.log" -ErrorAction SilentlyContinue | 
                        Select-String -Pattern "augment|error|exception" -CaseSensitive:$false | 
                        Select-Object -Last 10
        
        if ($augmentErrors) {
            Write-Host "`nAugment ile ilgili son hatalar:" -ForegroundColor Red
            $augmentErrors | ForEach-Object { Write-Host $_ -ForegroundColor White }
        } else {
            Write-Host "Augment ile ilgili hata bulunamadı." -ForegroundColor Green
        }
    }
}

# Extension host loglarını kontrol et
Write-Host "`n=== Extension Host Durumu ===" -ForegroundColor Cyan
$processes = Get-Process -Name "*Code*" -ErrorAction SilentlyContinue
if ($processes) {
    Write-Host "Çalışan VS Code işlemleri:" -ForegroundColor Green
    $processes | Format-Table Name, Id, CPU, WorkingSet -AutoSize
} else {
    Write-Host "VS Code çalışmıyor." -ForegroundColor Yellow
}

# Augment extension durumunu kontrol et
Write-Host "`n=== Augment Extension Durumu ===" -ForegroundColor Cyan
try {
    $extensions = code --list-extensions --show-versions | Select-String "augment"
    if ($extensions) {
        Write-Host "Yüklü Augment extension:" -ForegroundColor Green
        $extensions | ForEach-Object { Write-Host $_ -ForegroundColor White }
    } else {
        Write-Host "Augment extension bulunamadı!" -ForegroundColor Red
    }
} catch {
    Write-Host "Extension listesi alınamadı: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nLog kontrolü tamamlandı." -ForegroundColor Cyan

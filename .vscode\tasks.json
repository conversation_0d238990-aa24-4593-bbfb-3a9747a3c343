{"version": "2.0.0", "tasks": [{"label": "Restart Augment Extension", "type": "shell", "command": "code", "args": ["--disable-extension", "augmentcode.augment"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Enable Augment Extension", "type": "shell", "command": "code", "args": ["--enable-extension", "augmentcode.augment"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}
# Hastane projesi için güvenli VS Code başlatma scripti

param(
    [string]$HastanePath = "C:\Documents\ITB_SOURCE_4.8\Hastane",
    [switch]$NoAugment,
    [switch]$SafeMode
)

Write-Host "=== Hastane Projesi Güvenli Başlatma ===" -ForegroundColor Cyan

# Hastane klasörünün varlığını kontrol et
if (-not (Test-Path $HastanePath)) {
    Write-Host "HATA: Hastane klasörü bulunamadı: $HastanePath" -ForegroundColor Red
    exit 1
}

# Mevcut VS Code işlemlerini kapat
Write-Host "Mevcut VS Code işlemleri kapatılıyor..." -ForegroundColor Yellow
$codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
if ($codeProcesses) {
    $codeProcesses | Stop-Process -Force
    Write-Host "$(($codeProcesses).Count) VS Code işlemi kapatıldı." -ForegroundColor Gray
    Start-Sleep -Seconds 5
}

# Sistem kaynaklarını kontrol et
$memory = Get-WmiObject -Class Win32_OperatingSystem
$freeMemoryGB = [math]::Round($memory.FreePhysicalMemory/1MB, 2)
Write-Host "Mevcut boş RAM: $freeMemoryGB GB" -ForegroundColor Cyan

if ($freeMemoryGB -lt 2) {
    Write-Host "UYARI: Düşük RAM tespit edildi! Safe Mode önerilir." -ForegroundColor Red
    $SafeMode = $true
}

# Cache temizliği
Write-Host "VS Code cache temizleniyor..." -ForegroundColor Yellow
$cacheLocations = @(
    "$env:APPDATA\Code\User\workspaceStorage",
    "$env:APPDATA\Code\CachedExtensions"
)

foreach ($location in $cacheLocations) {
    if (Test-Path $location) {
        Get-ChildItem $location | Where-Object { 
            $_.Name -like "*hastane*" -or 
            $_.Name -like "*ITB*" 
        } | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Memory temizliği
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

if ($NoAugment) {
    Write-Host "Augment olmadan VS Code başlatılıyor..." -ForegroundColor Green
    Start-Process "code" -ArgumentList "`"$HastanePath`"", "--disable-extension", "augment.vscode-augment", "--new-window"
    Write-Host "VS Code Augment olmadan başlatıldı." -ForegroundColor Green
    exit 0
}

if ($SafeMode) {
    Write-Host "Safe Mode: Aşamalı başlatma yapılıyor..." -ForegroundColor Yellow
    
    # 1. Adım: Augment olmadan başlat
    Write-Host "1/3: VS Code Augment olmadan başlatılıyor..." -ForegroundColor Gray
    Start-Process "code" -ArgumentList "`"$HastanePath`"", "--disable-extension", "augment.vscode-augment", "--new-window"
    
    # VS Code'un tamamen yüklenmesini bekle
    Write-Host "VS Code'un yüklenmesi bekleniyor..." -ForegroundColor Gray
    Start-Sleep -Seconds 10
    
    # VS Code'un çalıştığını kontrol et
    $attempts = 0
    do {
        Start-Sleep -Seconds 2
        $codeRunning = Get-Process -Name "Code" -ErrorAction SilentlyContinue
        $attempts++
        Write-Host "." -NoNewline -ForegroundColor Gray
    } while (-not $codeRunning -and $attempts -lt 15)
    
    if (-not $codeRunning) {
        Write-Host "`nHATA: VS Code başlatılamadı!" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "`n2/3: VS Code başarıyla yüklendi." -ForegroundColor Green
    
    # 2. Adım: Augment'i yavaşça etkinleştir
    Write-Host "3/3: Augment eklentisi etkinleştiriliyor..." -ForegroundColor Gray
    Start-Sleep -Seconds 5
    code --enable-extension augment.vscode-augment
    
    Write-Host "Safe Mode başlatma tamamlandı!" -ForegroundColor Green
    
} else {
    # Normal başlatma
    Write-Host "Normal mod: VS Code başlatılıyor..." -ForegroundColor Green
    Start-Process "code" -ArgumentList "`"$HastanePath`"", "--new-window"
}

# Başlatma sonrası monitoring
Write-Host "`n=== Başlatma Sonrası Kontrol ===" -ForegroundColor Cyan
Write-Host "30 saniye boyunca VS Code durumu izleniyor..." -ForegroundColor Yellow

for ($i = 1; $i -le 6; $i++) {
    Start-Sleep -Seconds 5
    $codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
    
    if ($codeProcesses) {
        $processCount = $codeProcesses.Count
        $totalMemory = [math]::Round(($codeProcesses | Measure-Object WorkingSet -Sum).Sum / 1MB, 0)
        Write-Host "[$i/6] VS Code çalışıyor - İşlem: $processCount, RAM: $totalMemory MB" -ForegroundColor Green
    } else {
        Write-Host "[$i/6] UYARI: VS Code kapandı!" -ForegroundColor Red
        
        if ($i -eq 1) {
            Write-Host "Hemen yeniden başlatma deneniyor..." -ForegroundColor Yellow
            Start-Process "code" -ArgumentList "`"$HastanePath`"", "--disable-extension", "augment.vscode-augment", "--new-window"
        }
    }
}

$finalCheck = Get-Process -Name "Code" -ErrorAction SilentlyContinue
if ($finalCheck) {
    Write-Host "`n✅ BAŞARILI: VS Code stabil çalışıyor!" -ForegroundColor Green
    Write-Host "İşlem sayısı: $(($finalCheck).Count)" -ForegroundColor White
    Write-Host "Toplam RAM: $([math]::Round(($finalCheck | Measure-Object WorkingSet -Sum).Sum / 1MB, 0)) MB" -ForegroundColor White
} else {
    Write-Host "`n❌ SORUN: VS Code kapanmaya devam ediyor!" -ForegroundColor Red
    Write-Host "Çözüm önerileri:" -ForegroundColor Yellow
    Write-Host "1. .\safe-start-hastane.ps1 -NoAugment    # Augment olmadan çalıştır" -ForegroundColor White
    Write-Host "2. .\safe-start-hastane.ps1 -SafeMode     # Güvenli mod" -ForegroundColor White
    Write-Host "3. Bilgisayarı yeniden başlatın" -ForegroundColor White
}

Write-Host "`nScript tamamlandı." -ForegroundColor Cyan

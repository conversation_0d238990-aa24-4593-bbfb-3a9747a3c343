# VS Code crash tanılama scripti

param(
    [string]$HastanePath = "C:\Documents\ITB_SOURCE_4.8\Hastane"
)

Write-Host "=== VS Code Crash Tanılama ===" -ForegroundColor Cyan

# Sistem bilgilerini topla
Write-Host "`n1. Sistem Durumu:" -ForegroundColor Yellow
$os = Get-WmiObject -Class Win32_OperatingSystem
$totalRAM = [math]::Round($os.TotalVisibleMemorySize/1MB, 2)
$freeRAM = [math]::Round($os.FreePhysicalMemory/1MB, 2)
$usedRAM = $totalRAM - $freeRAM

Write-Host "   Toplam RAM: $totalRAM GB" -ForegroundColor White
Write-Host "   Kullanılan RAM: $usedRAM GB ($([math]::Round(($usedRAM/$totalRAM)*100, 1))%)" -ForegroundColor White
Write-Host "   Boş RAM: $freeRAM GB" -ForegroundColor White

if ($freeRAM -lt 2) {
    Write-Host "   ⚠️  UYARI: Düşük RAM!" -ForegroundColor Red
}

# Disk alanını kontrol et
Write-Host "`n2. Disk Durumu:" -ForegroundColor Yellow
$disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
foreach ($d in $disk) {
    $freeSpace = [math]::Round($d.FreeSpace/1GB, 2)
    $totalSpace = [math]::Round($d.Size/1GB, 2)
    $usedPercent = [math]::Round((($totalSpace - $freeSpace) / $totalSpace) * 100, 1)
    
    Write-Host "   $($d.DeviceID) Boş: $freeSpace GB / $totalSpace GB (Kullanım: $usedPercent%)" -ForegroundColor White
    
    if ($freeSpace -lt 5) {
        Write-Host "   ⚠️  UYARI: $($d.DeviceID) diskinde az yer!" -ForegroundColor Red
    }
}

# VS Code log dosyalarını kontrol et
Write-Host "`n3. VS Code Log Analizi:" -ForegroundColor Yellow
$logPath = "$env:APPDATA\Code\logs"

if (Test-Path $logPath) {
    $latestLogDir = Get-ChildItem $logPath | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    
    if ($latestLogDir) {
        Write-Host "   Son log dizini: $($latestLogDir.Name)" -ForegroundColor White
        
        # Main log dosyasını kontrol et
        $mainLog = Join-Path $latestLogDir.FullName "main.log"
        if (Test-Path $mainLog) {
            $errors = Get-Content $mainLog | Select-String -Pattern "ERROR|FATAL|crash|exception" -CaseSensitive:$false | Select-Object -Last 5
            
            if ($errors) {
                Write-Host "   Son hatalar:" -ForegroundColor Red
                $errors | ForEach-Object { Write-Host "     $_" -ForegroundColor Gray }
            } else {
                Write-Host "   Main log'da hata bulunamadı." -ForegroundColor Green
            }
        }
        
        # Extension host log'unu kontrol et
        $extHostLog = Join-Path $latestLogDir.FullName "exthost\exthost.log"
        if (Test-Path $extHostLog) {
            $extErrors = Get-Content $extHostLog | Select-String -Pattern "augment|ERROR|FATAL|crash" -CaseSensitive:$false | Select-Object -Last 5
            
            if ($extErrors) {
                Write-Host "   Extension host hataları:" -ForegroundColor Red
                $extErrors | ForEach-Object { Write-Host "     $_" -ForegroundColor Gray }
            } else {
                Write-Host "   Extension host log'da Augment hatası bulunamadı." -ForegroundColor Green
            }
        }
    }
} else {
    Write-Host "   VS Code log dizini bulunamadı." -ForegroundColor Gray
}

# Windows Event Log kontrol et
Write-Host "`n4. Windows Event Log Kontrol:" -ForegroundColor Yellow
try {
    $appCrashes = Get-WinEvent -FilterHashtable @{LogName='Application'; Level=2; StartTime=(Get-Date).AddHours(-2)} -ErrorAction SilentlyContinue | 
                  Where-Object { $_.ProcessName -like "*Code*" -or $_.Message -like "*Code*" } | 
                  Select-Object -First 3
    
    if ($appCrashes) {
        Write-Host "   Son 2 saatte VS Code crash'leri:" -ForegroundColor Red
        $appCrashes | ForEach-Object { 
            Write-Host "     $($_.TimeCreated): $($_.LevelDisplayName) - $($_.Message.Substring(0, [Math]::Min(100, $_.Message.Length)))..." -ForegroundColor Gray 
        }
    } else {
        Write-Host "   Son 2 saatte VS Code crash'i bulunamadı." -ForegroundColor Green
    }
} catch {
    Write-Host "   Event log okunamadı: $($_.Exception.Message)" -ForegroundColor Gray
}

# Hastane klasörü analizi
Write-Host "`n5. Hastane Klasörü Analizi:" -ForegroundColor Yellow
if (Test-Path $HastanePath) {
    $fileCount = (Get-ChildItem $HastanePath -Recurse -File | Measure-Object).Count
    $dirCount = (Get-ChildItem $HastanePath -Recurse -Directory | Measure-Object).Count
    $totalSize = [math]::Round((Get-ChildItem $HastanePath -Recurse -File | Measure-Object Length -Sum).Sum / 1GB, 2)
    
    Write-Host "   Toplam dosya: $fileCount" -ForegroundColor White
    Write-Host "   Toplam klasör: $dirCount" -ForegroundColor White
    Write-Host "   Toplam boyut: $totalSize GB" -ForegroundColor White
    
    # Büyük dosyaları bul
    $largeFiles = Get-ChildItem $HastanePath -Recurse -File | Where-Object { $_.Length -gt 10MB } | Sort-Object Length -Descending | Select-Object -First 5
    if ($largeFiles) {
        Write-Host "   En büyük dosyalar:" -ForegroundColor Yellow
        $largeFiles | ForEach-Object { 
            $sizeMB = [math]::Round($_.Length / 1MB, 1)
            Write-Host "     $($_.Name): $sizeMB MB" -ForegroundColor Gray
        }
    }
    
    if ($fileCount -gt 30000) {
        Write-Host "   ⚠️  UYARI: Çok fazla dosya! ($fileCount)" -ForegroundColor Red
    }
    if ($totalSize -gt 5) {
        Write-Host "   ⚠️  UYARI: Büyük proje boyutu! ($totalSize GB)" -ForegroundColor Red
    }
} else {
    Write-Host "   Hastane klasörü bulunamadı: $HastanePath" -ForegroundColor Red
}

# Çalışan VS Code işlemleri
Write-Host "`n6. Mevcut VS Code İşlemleri:" -ForegroundColor Yellow
$codeProcesses = Get-Process -Name "*Code*" -ErrorAction SilentlyContinue
if ($codeProcesses) {
    Write-Host "   Çalışan işlemler:" -ForegroundColor White
    $codeProcesses | ForEach-Object {
        $memoryMB = [math]::Round($_.WorkingSet / 1MB, 0)
        Write-Host "     $($_.ProcessName) (PID: $($_.Id)): $memoryMB MB" -ForegroundColor Gray
    }
} else {
    Write-Host "   VS Code işlemi çalışmıyor." -ForegroundColor Gray
}

# Öneriler
Write-Host "`n=== ÖNERİLER ===" -ForegroundColor Cyan

if ($freeRAM -lt 2) {
    Write-Host "🔴 YÜKSEK ÖNCELİK: RAM yetersiz!" -ForegroundColor Red
    Write-Host "   - Diğer uygulamaları kapatın" -ForegroundColor White
    Write-Host "   - .\safe-start-hastane.ps1 -SafeMode kullanın" -ForegroundColor White
}

if ($fileCount -gt 25000) {
    Write-Host "🟡 ORTA ÖNCELİK: Çok büyük proje!" -ForegroundColor Yellow
    Write-Host "   - .\safe-start-hastane.ps1 -NoAugment ile başlayın" -ForegroundColor White
    Write-Host "   - Gereksiz klasörleri .gitignore'a ekleyin" -ForegroundColor White
}

Write-Host "🟢 GENEL ÖNERİLER:" -ForegroundColor Green
Write-Host "   - Bilgisayarı yeniden başlatın" -ForegroundColor White
Write-Host "   - VS Code'u güncelleyin" -ForegroundColor White
Write-Host "   - .\safe-start-hastane.ps1 -SafeMode deneyin" -ForegroundColor White

Write-Host "`nTanılama tamamlandı." -ForegroundColor Cyan

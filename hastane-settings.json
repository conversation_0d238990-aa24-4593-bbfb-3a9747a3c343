{
  // Büyük proje için özel VS Code ayarları (Hastane klasörü)
  
  // Terminal ayarları
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.profiles.windows": {
    "PowerShell": {
      "source": "PowerShell",
      "icon": "terminal-powershell",
      "args": ["-NoLogo", "-NoProfile"]
    }
  },
  "terminal.integrated.shellIntegration.enabled": false,
  "terminal.integrated.enableMultiLinePasteWarning": false,
  "terminal.integrated.persistentSessionReviveProcess": "never",
  "terminal.integrated.enablePersistentSessions": false,
  
  // Büyük proje için performans ayarları
  "extensions.autoUpdate": false,
  "extensions.autoCheckUpdates": false,
  "workbench.startupEditor": "none",
  "window.restoreWindows": "none",
  
  // Dosya izleme sınırlamaları (ÇOK ÖNEMLİ)
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/tmp/**": true,
    "**/temp/**": true,
    "**/bower_components/**": true,
    "**/dist/**": true,
    "**/build/**": true,
    "**/bin/**": true,
    "**/obj/**": true,
    "**/packages/**": true,
    "**/Debug/**": true,
    "**/Release/**": true,
    "**/.vs/**": true,
    "**/logs/**": true,
    "**/log/**": true,
    "**/*.log": true,
    "**/audio/**": true,
    "**/Bootstrap----/**": true,
    "**/ckeditor/**": true
  },
  
  // Arama sınırlamaları
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/bin": true,
    "**/obj": true,
    "**/packages": true,
    "**/Debug": true,
    "**/Release": true,
    "**/.vs": true,
    "**/logs": true,
    "**/audio": true,
    "**/Bootstrap----": true,
    "**/ckeditor": true,
    "**/*.dll": true,
    "**/*.exe": true,
    "**/*.pdb": true
  },
  
  // Augment eklentisi için BÜYÜK PROJE ayarları
  "augment.autoStart": false,
  "augment.enableLogging": true,
  "augment.maxMemoryUsage": 8192,
  "augment.restartOnError": false,
  "augment.timeout": 120000,
  "augment.retryAttempts": 1,
  "augment.indexingTimeout": 300000,
  "augment.maxFileSize": 1048576,
  "augment.excludePatterns": [
    "**/bin/**",
    "**/obj/**",
    "**/packages/**",
    "**/Debug/**",
    "**/Release/**",
    "**/.vs/**",
    "**/logs/**",
    "**/audio/**",
    "**/Bootstrap----/**",
    "**/ckeditor/**",
    "**/*.dll",
    "**/*.exe",
    "**/*.pdb",
    "**/*.log"
  ],
  
  // Editor ayarları
  "editor.formatOnSave": false,
  "editor.codeActionsOnSave": {},
  "files.autoSave": "off",
  
  // Git ayarları
  "git.autofetch": false,
  "git.confirmSync": false,
  "git.enabled": false,
  
  // Diğer performans ayarları
  "search.followSymlinks": false,
  "typescript.surveys.enabled": false,
  "telemetry.telemetryLevel": "off",
  "update.mode": "manual",
  "workbench.settings.enableNaturalLanguageSearch": false,
  
  // IntelliSense sınırlamaları
  "typescript.disableAutomaticTypeAcquisition": true,
  "typescript.suggest.autoImports": "off",
  "javascript.suggest.autoImports": "off",
  
  // Dosya limitleri
  "search.maxResults": 1000,
  "explorer.incrementalNaming": "disabled"
}

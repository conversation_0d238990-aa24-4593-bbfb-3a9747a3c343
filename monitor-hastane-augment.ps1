# Hastane projesi Augment monitoring scripti

param(
    [int]$Duration = 300,  # 5 dakika
    [int]$Interval = 10    # 10 saniye
)

Write-Host "=== Hastane Projesi Augment Monitoring ===" -ForegroundColor Cyan
Write-Host "Monitoring süresi: $Duration saniye ($(($Duration/60).ToString('F1')) dakika)" -ForegroundColor Yellow
Write-Host "Kontrol aralığı: $Interval saniye" -ForegroundColor Yellow
Write-Host "Başlangıç zamanı: $(Get-Date -Format 'HH:mm:ss')" -ForegroundColor Green

$startTime = Get-Date
$endTime = $startTime.AddSeconds($Duration)
$restartCount = 0
$maxMemoryUsage = 0

Write-Host "`nZaman`t`tVS Code`tRAM(MB)`tCPU%`tAugment Durumu" -ForegroundColor Cyan
Write-Host "----`t`t-------`t-------`t----`t--------------" -ForegroundColor Gray

while ((Get-Date) -lt $endTime) {
    $currentTime = Get-Date -Format 'HH:mm:ss'
    
    # VS Code işlemlerini kontrol et
    $codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
    
    if ($codeProcesses) {
        $totalMemory = ($codeProcesses | Measure-Object WorkingSet -Sum).Sum / 1MB
        $totalCpu = ($codeProcesses | Measure-Object CPU -Sum).Sum
        $processCount = $codeProcesses.Count
        
        # Maximum memory kullanımını takip et
        if ($totalMemory -gt $maxMemoryUsage) {
            $maxMemoryUsage = $totalMemory
        }
        
        # Augment extension durumunu kontrol et (basit kontrol)
        $augmentStatus = "Çalışıyor"
        if ($totalMemory -gt 2000) {
            $augmentStatus = "Yüksek RAM"
        }
        if ($processCount -gt 10) {
            $augmentStatus = "Çok İşlem"
        }
        
        # Yeniden başlatma kontrolü (işlem sayısı ani değişimi)
        if ($processCount -lt 3) {
            $restartCount++
            $augmentStatus = "YENİDEN BAŞLADI!"
        }
        
        $memoryFormatted = $totalMemory.ToString("F0")
        $cpuFormatted = $totalCpu.ToString("F1")
        
        # Renkli output
        $color = "White"
        if ($totalMemory -gt 1500) { $color = "Yellow" }
        if ($totalMemory -gt 2500) { $color = "Red" }
        if ($augmentStatus -eq "YENİDEN BAŞLADI!") { $color = "Red" }
        
        Write-Host "$currentTime`t$processCount`t$memoryFormatted`t$cpuFormatted`t$augmentStatus" -ForegroundColor $color
        
    } else {
        Write-Host "$currentTime`tYOK`t0`t0`tVS Code Kapalı" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds $Interval
}

# Özet rapor
Write-Host "`n=== Monitoring Özeti ===" -ForegroundColor Cyan
Write-Host "Toplam süre: $(($Duration/60).ToString('F1')) dakika" -ForegroundColor White
Write-Host "Yeniden başlatma sayısı: $restartCount" -ForegroundColor $(if($restartCount -gt 0){"Red"}else{"Green"})
Write-Host "Maksimum RAM kullanımı: $($maxMemoryUsage.ToString('F0')) MB" -ForegroundColor White
Write-Host "Bitiş zamanı: $(Get-Date -Format 'HH:mm:ss')" -ForegroundColor Green

# Öneri
if ($restartCount -gt 2) {
    Write-Host "`nÖNERİ: Çok fazla yeniden başlatma tespit edildi!" -ForegroundColor Red
    Write-Host "Şu komutu çalıştırın: .\fix-hastane-augment.ps1 -SlowMode" -ForegroundColor Yellow
} elseif ($maxMemoryUsage -gt 3000) {
    Write-Host "`nÖNERİ: Yüksek RAM kullanımı tespit edildi!" -ForegroundColor Yellow
    Write-Host "Şu komutu çalıştırın: .\fix-hastane-augment.ps1 -ResetIndex" -ForegroundColor Yellow
} else {
    Write-Host "`nDurum: Normal çalışıyor ✓" -ForegroundColor Green
}

Write-Host "`nMonitoring tamamlandı." -ForegroundColor Cyan

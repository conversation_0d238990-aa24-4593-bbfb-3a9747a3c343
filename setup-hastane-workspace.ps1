# Hastane klasörü için özel VS Code workspace kurulumu

param(
    [string]$HastanePath = "C:\Documents\ITB_SOURCE_4.8\Hastane",
    [switch]$Force
)

Write-Host "=== Hastane Klasörü VS Code Kurulumu ===" -ForegroundColor Cyan

# Hastane klasörünün varlığını kontrol et
if (-not (Test-Path $HastanePath)) {
    Write-Host "HATA: Hastane klasörü bulunamadı: $HastanePath" -ForegroundColor Red
    exit 1
}

Write-Host "Hastane klasörü: $HastanePath" -ForegroundColor Green

# Dosya sayısını kontrol et
$fileCount = (Get-ChildItem $HastanePath -Recurse | Measure-Object).Count
Write-Host "Toplam dosya sayısı: $fileCount" -ForegroundColor Yellow

if ($fileCount -gt 20000) {
    Write-Host "UYARI: Çok büyük proje ($fileCount dosya). Özel ayarlar uygulanacak." -ForegroundColor Yellow
}

# .vscode klasörünü oluştur
$vscodeDir = Join-Path $HastanePath ".vscode"
if (-not (Test-Path $vscodeDir)) {
    New-Item -ItemType Directory -Path $vscodeDir -Force | Out-Null
    Write-Host ".vscode klasörü oluşturuldu." -ForegroundColor Green
}

# Özel ayarları kopyala
$settingsFile = Join-Path $vscodeDir "settings.json"
if ((Test-Path $settingsFile) -and -not $Force) {
    Write-Host "settings.json zaten mevcut. -Force parametresi ile üzerine yazabilirsiniz." -ForegroundColor Yellow
} else {
    Copy-Item "hastane-settings.json" $settingsFile -Force
    Write-Host "Büyük proje ayarları uygulandı: $settingsFile" -ForegroundColor Green
}

# .gitignore oluştur (performans için)
$gitignoreFile = Join-Path $HastanePath ".gitignore"
if (-not (Test-Path $gitignoreFile)) {
    @"
# VS Code
.vscode/
*.code-workspace

# Build outputs
bin/
obj/
Debug/
Release/
packages/

# Logs
logs/
*.log

# Audio files
audio/

# Large libraries
Bootstrap----/
ckeditor/

# Temporary files
tmp/
temp/
"@ | Out-File -FilePath $gitignoreFile -Encoding UTF8
    Write-Host ".gitignore oluşturuldu." -ForegroundColor Green
}

# VS Code'u kapat
Write-Host "VS Code kapatılıyor..." -ForegroundColor Yellow
Get-Process -Name "Code" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 3

# Augment cache'ini temizle
Write-Host "Augment cache temizleniyor..." -ForegroundColor Yellow
$augmentCache = "$env:APPDATA\Code\User\workspaceStorage"
if (Test-Path $augmentCache) {
    Get-ChildItem $augmentCache | Where-Object { $_.Name -like "*hastane*" -or $_.Name -like "*ITB*" } | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
}

# Memory'yi temizle
Write-Host "Sistem memory'si temizleniyor..." -ForegroundColor Yellow
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

# VS Code'u Hastane klasörü ile başlat
Write-Host "VS Code Hastane klasörü ile başlatılıyor..." -ForegroundColor Green
Write-Host "UYARI: İlk açılışta indexleme uzun sürebilir. Sabırlı olun!" -ForegroundColor Yellow

Start-Process "code" -ArgumentList "`"$HastanePath`"", "--new-window" -NoNewWindow

Write-Host "`n=== Kurulum Tamamlandı ===" -ForegroundColor Cyan
Write-Host "Önemli Notlar:" -ForegroundColor Yellow
Write-Host "1. İlk açılışta Augment indexleme 5-10 dakika sürebilir" -ForegroundColor White
Write-Host "2. Eğer yeniden başlarsa, indexleme tamamlanana kadar bekleyin" -ForegroundColor White
Write-Host "3. Büyük dosyaları (>1MB) indexlemez" -ForegroundColor White
Write-Host "4. bin/, obj/, packages/ klasörleri hariç tutuldu" -ForegroundColor White
Write-Host "`nSorun yaşarsanız: .\fix-hastane-augment.ps1 çalıştırın" -ForegroundColor Cyan

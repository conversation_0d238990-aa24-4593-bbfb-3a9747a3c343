# Acil durum: Augment'i tamamen devre dışı bırak

Write-Host "=== ACİL DURUM: AUGMENT DEVRE DIŞI ===" -ForegroundColor Red

# Mevcut durumu kontrol et
$codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
if ($codeProcesses) {
    Write-Host "VS Code çalışıyor. İşlem sayısı: $(($codeProcesses).Count)" -ForegroundColor Yellow
} else {
    Write-Host "VS Code çalışmıyor." -ForegroundColor Red
}

# Augment'i kalıcı olarak devre dışı bırak
Write-Host "Augment eklentisi kalıcı olarak devre dışı bırakılıyor..." -ForegroundColor Yellow

# Global ayarlarda Augment'i devre dışı bırak
$globalSettingsPath = "$env:APPDATA\Code\User\settings.json"
if (Test-Path $globalSettingsPath) {
    $settings = Get-Content $globalSettingsPath -Raw | ConvertFrom-Json
    
    # Augment'i devre dışı bırak
    $settings | Add-Member -NotePropertyName "augment.enabled" -NotePropertyValue $false -Force
    $settings | Add-Member -NotePropertyName "augment.autoStart" -NotePropertyValue $false -Force
    
    # Ayarları kaydet
    $settings | ConvertTo-Json -Depth 10 | Out-File -FilePath $globalSettingsPath -Encoding UTF8 -Force
    Write-Host "✅ Global ayarlarda Augment devre dışı bırakıldı." -ForegroundColor Green
}

# Hastane klasörü ayarlarında da Augment'i devre dışı bırak
$hastaneSettingsPath = "C:\Documents\ITB_SOURCE_4.8\Hastane\.vscode\settings.json"
if (Test-Path $hastaneSettingsPath) {
    $hastaneSettings = Get-Content $hastaneSettingsPath -Raw | ConvertFrom-Json
    
    $hastaneSettings | Add-Member -NotePropertyName "augment.enabled" -NotePropertyValue $false -Force
    $hastaneSettings | Add-Member -NotePropertyName "augment.autoStart" -NotePropertyValue $false -Force
    
    $hastaneSettings | ConvertTo-Json -Depth 10 | Out-File -FilePath $hastaneSettingsPath -Encoding UTF8 -Force
    Write-Host "✅ Hastane ayarlarında Augment devre dışı bırakıldı." -ForegroundColor Green
}

# Extension'ı sistem seviyesinde devre dışı bırak
Write-Host "Extension sistem seviyesinde devre dışı bırakılıyor..." -ForegroundColor Yellow

# VS Code extension dizinini bul
$extensionDirs = @(
    "$env:USERPROFILE\.vscode\extensions",
    "$env:APPDATA\Code\User\extensions"
)

foreach ($extDir in $extensionDirs) {
    if (Test-Path $extDir) {
        $augmentDirs = Get-ChildItem $extDir | Where-Object { $_.Name -like "*augment*" }
        foreach ($augmentDir in $augmentDirs) {
            $disabledMarker = Join-Path $augmentDir.FullName ".disabled"
            New-Item -ItemType File -Path $disabledMarker -Force | Out-Null
            Write-Host "✅ Devre dışı bırakıldı: $($augmentDir.Name)" -ForegroundColor Green
        }
    }
}

# Tüm Augment cache'ini temizle
Write-Host "Tüm Augment cache'i temizleniyor..." -ForegroundColor Yellow
$cacheLocations = @(
    "$env:APPDATA\Code\User\workspaceStorage",
    "$env:APPDATA\Code\CachedExtensions",
    "$env:APPDATA\Code\logs",
    "$env:LOCALAPPDATA\augment"
)

foreach ($location in $cacheLocations) {
    if (Test-Path $location) {
        if ($location -like "*workspaceStorage*") {
            Get-ChildItem $location -ErrorAction SilentlyContinue | Where-Object { 
                $_.Name -like "*augment*" -or $_.Name -like "*hastane*" -or $_.Name -like "*ITB*" 
            } | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
        } else {
            Remove-Item $location -Recurse -Force -ErrorAction SilentlyContinue
        }
        Write-Host "✅ Temizlendi: $location" -ForegroundColor Gray
    }
}

# Memory temizliği
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "`n=== ACİL DURUM ÇÖZÜMÜ TAMAMLANDI ===" -ForegroundColor Green
Write-Host "✅ Augment tamamen devre dışı bırakıldı" -ForegroundColor White
Write-Host "✅ Tüm cache'ler temizlendi" -ForegroundColor White
Write-Host "✅ Memory temizlendi" -ForegroundColor White

Write-Host "`nŞimdi VS Code stabil çalışmalı!" -ForegroundColor Cyan
Write-Host "Augment'i tekrar etkinleştirmek için:" -ForegroundColor Yellow
Write-Host "1. VS Code Settings'e gidin" -ForegroundColor White
Write-Host "2. 'augment.enabled' ayarını true yapın" -ForegroundColor White
Write-Host "3. VS Code'u yeniden başlatın" -ForegroundColor White

# Son kontrol
$finalProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
if ($finalProcesses) {
    $totalRAM = [math]::Round(($finalProcesses | Measure-Object WorkingSet -Sum).Sum / 1MB, 0)
    Write-Host "`n✅ VS Code çalışıyor: $(($finalProcesses).Count) işlem, $totalRAM MB RAM" -ForegroundColor Green
} else {
    Write-Host "`n❌ VS Code kapandı. Manuel başlatın:" -ForegroundColor Red
    Write-Host "code `"C:\Documents\ITB_SOURCE_4.8\Hastane`"" -ForegroundColor White
}

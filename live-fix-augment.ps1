# VS Code'u kapatmadan Augment sorununu çözme scripti

Write-Host "=== VS Code Canlı Augment Düzeltme ===" -ForegroundColor Cyan

# Mevcut VS Code işlemlerini kontrol et
$codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
if (-not $codeProcesses) {
    Write-Host "VS Code çalışmıyor!" -ForegroundColor Red
    exit 1
}

Write-Host "VS Code çalışıyor. İşlem sayısı: $(($codeProcesses).Count)" -ForegroundColor Green

# Hastane klasörü ayarlarını kontrol et
$hastaneSettingsPath = "C:\Documents\ITB_SOURCE_4.8\Hastane\.vscode\settings.json"
if (Test-Path $hastaneSettingsPath) {
    Write-Host "✅ Hastane ayarları mevcut." -ForegroundColor Green
} else {
    Write-Host "❌ Hastane ayarları eksik. Oluşturuluyor..." -ForegroundColor Yellow
    
    # .vscode klasörünü oluştur
    $vscodeDir = "C:\Documents\ITB_SOURCE_4.8\Hastane\.vscode"
    if (-not (Test-Path $vscodeDir)) {
        New-Item -ItemType Directory -Path $vscodeDir -Force | Out-Null
    }
    
    # Ayarları kopyala
    if (Test-Path "hastane-settings.json") {
        Copy-Item "hastane-settings.json" $hastaneSettingsPath -Force
        Write-Host "✅ Hastane ayarları oluşturuldu." -ForegroundColor Green
    }
}

# Global VS Code ayarlarına Augment optimizasyonları ekle
Write-Host "Global VS Code ayarları güncelleniyor..." -ForegroundColor Yellow
$globalSettingsPath = "$env:APPDATA\Code\User\settings.json"

if (Test-Path $globalSettingsPath) {
    $settings = Get-Content $globalSettingsPath -Raw | ConvertFrom-Json
    
    # Augment ayarlarını ekle/güncelle
    $settings | Add-Member -NotePropertyName "augment.autoStart" -NotePropertyValue $false -Force
    $settings | Add-Member -NotePropertyName "augment.enableLogging" -NotePropertyValue $true -Force
    $settings | Add-Member -NotePropertyName "augment.maxMemoryUsage" -NotePropertyValue 4096 -Force
    $settings | Add-Member -NotePropertyName "augment.restartOnError" -NotePropertyValue $false -Force
    $settings | Add-Member -NotePropertyName "augment.timeout" -NotePropertyValue 120000 -Force
    $settings | Add-Member -NotePropertyName "augment.indexingTimeout" -NotePropertyValue 300000 -Force
    
    # Dosya izleme sınırlamaları
    if (-not $settings."files.watcherExclude") {
        $settings | Add-Member -NotePropertyName "files.watcherExclude" -NotePropertyValue @{} -Force
    }
    
    $watcherExclude = @{
        "**/.git/objects/**" = $true
        "**/node_modules/**" = $true
        "**/bin/**" = $true
        "**/obj/**" = $true
        "**/packages/**" = $true
        "**/Debug/**" = $true
        "**/Release/**" = $true
        "**/.vs/**" = $true
        "**/logs/**" = $true
        "**/audio/**" = $true
        "**/Bootstrap----/**" = $true
        "**/ckeditor/**" = $true
        "**/*.dll" = $true
        "**/*.exe" = $true
        "**/*.pdb" = $true
    }
    
    foreach ($key in $watcherExclude.Keys) {
        $settings."files.watcherExclude" | Add-Member -NotePropertyName $key -NotePropertyValue $watcherExclude[$key] -Force
    }
    
    # Ayarları kaydet
    $settings | ConvertTo-Json -Depth 10 | Out-File -FilePath $globalSettingsPath -Encoding UTF8 -Force
    Write-Host "✅ Global ayarlar güncellendi." -ForegroundColor Green
}

# Augment cache'ini temizle (sadece eski cache'ler)
Write-Host "Eski Augment cache'leri temizleniyor..." -ForegroundColor Yellow
$workspaceStorage = "$env:APPDATA\Code\User\workspaceStorage"
if (Test-Path $workspaceStorage) {
    Get-ChildItem $workspaceStorage | Where-Object { 
        $_.LastWriteTime -lt (Get-Date).AddHours(-1) -and 
        ($_.Name -like "*hastane*" -or $_.Name -like "*ITB*")
    } | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Eski cache'ler temizlendi." -ForegroundColor Green
}

# Memory temizliği
Write-Host "Memory temizliği yapılıyor..." -ForegroundColor Yellow
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

# VS Code işlemlerini tekrar kontrol et
$newCodeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
if ($newCodeProcesses) {
    $totalMemory = [math]::Round(($newCodeProcesses | Measure-Object WorkingSet -Sum).Sum / 1MB, 0)
    Write-Host "✅ VS Code hala çalışıyor. Toplam RAM: $totalMemory MB" -ForegroundColor Green
    
    if ($totalMemory -gt 3000) {
        Write-Host "⚠️  Yüksek RAM kullanımı tespit edildi!" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ VS Code kapandı!" -ForegroundColor Red
}

Write-Host "`n=== Canlı Düzeltme Tamamlandı ===" -ForegroundColor Cyan
Write-Host "Yapılan işlemler:" -ForegroundColor White
Write-Host "✅ Hastane klasörü ayarları oluşturuldu/kontrol edildi" -ForegroundColor Gray
Write-Host "✅ Global VS Code ayarları optimize edildi" -ForegroundColor Gray
Write-Host "✅ Eski cache'ler temizlendi" -ForegroundColor Gray
Write-Host "✅ Memory temizliği yapıldı" -ForegroundColor Gray

Write-Host "`nÖneriler:" -ForegroundColor Yellow
Write-Host "1. VS Code'da Ctrl+Shift+P > 'Reload Window' yapın" -ForegroundColor White
Write-Host "2. Eğer kapanırsa, .\safe-start-hastane.ps1 -SafeMode kullanın" -ForegroundColor White
Write-Host "3. Augment indexleme tamamlanana kadar sabırlı olun" -ForegroundColor White

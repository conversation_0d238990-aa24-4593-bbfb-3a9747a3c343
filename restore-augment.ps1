# Augment'i geri etkinleştirme ve düzeltme scripti

Write-Host "=== AUGMENT GERİ ETKİNLEŞTİRME ===" -ForegroundColor Cyan

# VS Code'un çalıştığını kontrol et
$codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
if (-not $codeProcesses) {
    Write-Host "VS Code çalışmıyor! Önce VS Code'u başlatın." -ForegroundColor Red
    exit 1
}

Write-Host "VS Code çalışıyor. İşlem sayısı: $(($codeProcesses).Count)" -ForegroundColor Green

# 1. Extension'ın disabled marker'ını kaldır
Write-Host "1. Augment extension'ı yeniden etkinleştiriliyor..." -ForegroundColor Yellow

$extensionDirs = @(
    "$env:USERPROFILE\.vscode\extensions",
    "$env:APPDATA\Code\User\extensions"
)

foreach ($extDir in $extensionDirs) {
    if (Test-Path $extDir) {
        $augmentDirs = Get-ChildItem $extDir | Where-Object { $_.Name -like "*augment*" }
        foreach ($augmentDir in $augmentDirs) {
            $disabledMarker = Join-Path $augmentDir.FullName ".disabled"
            if (Test-Path $disabledMarker) {
                Remove-Item $disabledMarker -Force
                Write-Host "✅ Etkinleştirildi: $($augmentDir.Name)" -ForegroundColor Green
            }
        }
    }
}

# 2. Global ayarlarda Augment'i etkinleştir ama KONSERVATIF ayarlarla
Write-Host "2. Global ayarlar güncelleniyor..." -ForegroundColor Yellow
$globalSettingsPath = "$env:APPDATA\Code\User\settings.json"

if (Test-Path $globalSettingsPath) {
    $settings = Get-Content $globalSettingsPath -Raw | ConvertFrom-Json
    
    # Augment'i etkinleştir ama konservatif ayarlarla
    $settings | Add-Member -NotePropertyName "augment.enabled" -NotePropertyValue $true -Force
    $settings | Add-Member -NotePropertyName "augment.autoStart" -NotePropertyValue $true -Force
    $settings | Add-Member -NotePropertyName "augment.enableLogging" -NotePropertyValue $true -Force
    $settings | Add-Member -NotePropertyName "augment.maxMemoryUsage" -NotePropertyValue 2048 -Force  # 2GB limit
    $settings | Add-Member -NotePropertyName "augment.restartOnError" -NotePropertyValue $false -Force
    $settings | Add-Member -NotePropertyName "augment.timeout" -NotePropertyValue 60000 -Force  # 1 dakika
    $settings | Add-Member -NotePropertyName "augment.indexingTimeout" -NotePropertyValue 180000 -Force  # 3 dakika
    $settings | Add-Member -NotePropertyName "augment.retryAttempts" -NotePropertyValue 2 -Force
    
    # Ayarları kaydet
    $settings | ConvertTo-Json -Depth 10 | Out-File -FilePath $globalSettingsPath -Encoding UTF8 -Force
    Write-Host "✅ Global ayarlar güncellendi (konservatif)." -ForegroundColor Green
}

# 3. Hastane klasörü ayarlarını düzelt (JSON formatında)
Write-Host "3. Hastane klasörü ayarları düzeltiliyor..." -ForegroundColor Yellow
$hastaneSettingsPath = "C:\Documents\ITB_SOURCE_4.8\Hastane\.vscode\settings.json"

# Yeni, temiz JSON ayarları oluştur
$hastaneSettings = @{
    "augment.enabled" = $true
    "augment.autoStart" = $true
    "augment.enableLogging" = $true
    "augment.maxMemoryUsage" = 2048
    "augment.restartOnError" = $false
    "augment.timeout" = 60000
    "augment.indexingTimeout" = 180000
    "augment.retryAttempts" = 2
    "files.watcherExclude" = @{
        "**/.git/objects/**" = $true
        "**/node_modules/**" = $true
        "**/bin/**" = $true
        "**/obj/**" = $true
        "**/packages/**" = $true
        "**/Debug/**" = $true
        "**/Release/**" = $true
        "**/.vs/**" = $true
        "**/logs/**" = $true
        "**/audio/**" = $true
        "**/Bootstrap----/**" = $true
        "**/ckeditor/**" = $true
        "**/*.dll" = $true
        "**/*.exe" = $true
        "**/*.pdb" = $true
    }
    "search.exclude" = @{
        "**/bin" = $true
        "**/obj" = $true
        "**/packages" = $true
        "**/Debug" = $true
        "**/Release" = $true
        "**/.vs" = $true
        "**/logs" = $true
        "**/audio" = $true
        "**/Bootstrap----" = $true
        "**/ckeditor" = $true
        "**/*.dll" = $true
        "**/*.exe" = $true
        "**/*.pdb" = $true
    }
    "terminal.integrated.defaultProfile.windows" = "PowerShell"
    "extensions.autoUpdate" = $false
    "workbench.startupEditor" = "none"
}

# JSON olarak kaydet
$hastaneSettings | ConvertTo-Json -Depth 10 | Out-File -FilePath $hastaneSettingsPath -Encoding UTF8 -Force
Write-Host "✅ Hastane ayarları düzeltildi (temiz JSON)." -ForegroundColor Green

# 4. Sadece eski cache'leri temizle, yenilerini bırak
Write-Host "4. Sadece eski cache'ler temizleniyor..." -ForegroundColor Yellow
$workspaceStorage = "$env:APPDATA\Code\User\workspaceStorage"
if (Test-Path $workspaceStorage) {
    # Sadece 1 saatten eski cache'leri temizle
    Get-ChildItem $workspaceStorage | Where-Object { 
        $_.LastWriteTime -lt (Get-Date).AddHours(-1) -and 
        ($_.Name -like "*hastane*" -or $_.Name -like "*ITB*" -or $_.Name -like "*augment*")
    } | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Eski cache'ler temizlendi." -ForegroundColor Green
}

# 5. Memory temizliği
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "`n=== AUGMENT GERİ ETKİNLEŞTİRİLDİ ===" -ForegroundColor Green
Write-Host "✅ Extension etkinleştirildi" -ForegroundColor White
Write-Host "✅ Konservatif ayarlar uygulandı" -ForegroundColor White
Write-Host "✅ Hastane ayarları düzeltildi" -ForegroundColor White
Write-Host "✅ Eski cache'ler temizlendi" -ForegroundColor White

Write-Host "`nŞimdi VS Code'da:" -ForegroundColor Cyan
Write-Host "1. Ctrl+Shift+P tuşlarına basın" -ForegroundColor White
Write-Host "2. 'Developer: Reload Window' yazın ve Enter'a basın" -ForegroundColor White
Write-Host "3. Augment'in yavaşça başlamasını bekleyin (2-3 dakika)" -ForegroundColor White

Write-Host "`nKonservatif ayarlar:" -ForegroundColor Yellow
Write-Host "- Memory limit: 2GB (önceden 8GB)" -ForegroundColor Gray
Write-Host "- Timeout: 1 dakika (önceden 5 dakika)" -ForegroundColor Gray
Write-Host "- Indexing timeout: 3 dakika (önceden 5 dakika)" -ForegroundColor Gray
Write-Host "- Auto-restart: Kapalı" -ForegroundColor Gray

# Son durum kontrolü
$finalProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
if ($finalProcesses) {
    $totalRAM = [math]::Round(($finalProcesses | Measure-Object WorkingSet -Sum).Sum / 1MB, 0)
    Write-Host "`n✅ VS Code çalışıyor: $(($finalProcesses).Count) işlem, $totalRAM MB RAM" -ForegroundColor Green
} else {
    Write-Host "`n❌ VS Code kapandı!" -ForegroundColor Red
}
